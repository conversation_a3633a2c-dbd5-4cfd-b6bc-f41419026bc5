import type { DragEndEvent } from "@dnd-kit/core";
import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import {
	sessionOptions,
	sessionOptionsByClientAndTurn,
	sessionOptionsByWorkerAndTurn,
} from "../../hooks/session-options";
import useBulkCreateSessions from "../../hooks/use-bulk-create-sessions";
import useBulkDeleteSessions from "../../hooks/use-bulk-delete-sessions";
import useDeleteSession from "../../hooks/use-delete-session";
import useUpdateSession from "../../hooks/use-update-session";
import { sessionStore } from "../../store/session";

export interface SessionScheduleGridProps {
	schedule: Schedule;
	turn: Turn;
}

interface TimeSlot {
	start: number;
	end: number;
	label: string;
}

interface SessionModalData {
	dayIndex: number;
	timeIndex: number;
	isOpen: boolean;
}

export default function useSessionScheduleGrid({
	schedule,
	turn,
}: SessionScheduleGridProps) {
	const svc = useService();
	const { selectedClient, selectedWorker, seeAll, showNames } =
		useStore(sessionStore);
	const { mutate: bulkCreateSessions } = useBulkCreateSessions();
	const { mutate: bulkDeleteSessions } = useBulkDeleteSessions();
	const { mutate: updateSession } = useUpdateSession();
	const { mutate: deleteSession } = useDeleteSession();
	const [sessionModal, setSessionModal] = useState<SessionModalData>({
		dayIndex: -1,
		timeIndex: -1,
		isOpen: false,
	});

	// Use filtered queries based on selected client or worker
	const { data: sessionsByClient } = useQuery({
		...sessionOptionsByClientAndTurn(svc, selectedClient?.id || "", turn.id),
		enabled: !!selectedClient && !!turn.id && !seeAll,
	});

	const { data: sessionsByWorker } = useQuery({
		...sessionOptionsByWorkerAndTurn(svc, selectedWorker?.id || "", turn.id),
		enabled: !!selectedWorker && !!turn.id && !seeAll,
	});

	// Query for all sessions when "see all" is enabled
	const { data: allSessionsData, error } = useQuery({
		...sessionOptions(svc),
		enabled: seeAll,
	});

	// Filter all sessions by current turn when "see all" is enabled
	const allSessionsForTurn = seeAll
		? (allSessionsData || []).filter((session) => session.turnId === turn.id)
		: [];

	// Use the appropriate sessions data based on what's selected
	const sessions = seeAll
		? allSessionsForTurn
		: selectedClient
			? sessionsByClient
			: selectedWorker
				? sessionsByWorker
				: [];

	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${mins.toString().padStart(2, "0")} ${period}`;
	};

	const generateTimeSlots = (): TimeSlot[] => {
		const slots: TimeSlot[] = [];
		const sessionDuration = schedule.sessionDuration;
		const breakDuration = schedule.breakDuration;

		// Convert start and end times to minutes
		const startTimeInMinutes =
			Math.floor(turn.startTime / 100) * 60 + (turn.startTime % 100);
		const endTimeInMinutes =
			Math.floor(turn.endTime / 100) * 60 + (turn.endTime % 100);

		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	const getSessionForSlot = (dayIndex: number, timeIndex: number) => {
		return sessions?.find(
			(session) => session.day === dayIndex && session.time === timeIndex,
		);
	};

	const getSessionsForSlot = (dayIndex: number, timeIndex: number) => {
		return (
			sessions?.filter(
				(session) => session.day === dayIndex && session.time === timeIndex,
			) || []
		);
	};

	const handleCellClick = (dayIndex: number, timeIndex: number) => {
		setSessionModal({
			dayIndex,
			timeIndex,
			isOpen: true,
		});
	};

	const handleModalClose = () => {
		setSessionModal({
			dayIndex: -1,
			timeIndex: -1,
			isOpen: false,
		});
	};

	// Helper functions for day-level operations
	const getSessionsForDay = (dayIndex: number) => {
		return sessions?.filter((session) => session.day === dayIndex) || [];
	};

	const getBusySessionsForDay = (dayIndex: number) => {
		return getSessionsForDay(dayIndex).filter(
			(session) => session.client.id === "0",
		);
	};

	const hasAvailableSlotsForDay = (dayIndex: number) => {
		const daySessions = getSessionsForDay(dayIndex);
		return daySessions.length < timeSlots.length;
	};

	const hasBusySessionsForDay = (dayIndex: number) => {
		return getBusySessionsForDay(dayIndex).length > 0;
	};

	const handleMakeDayBusy = (dayIndex: number) => {
		if (!selectedWorker) {
			toast.error(
				"Debe seleccionar un trabajador para marcar el día como ocupado",
			);
			return;
		}

		// Get all time slots that don't have sessions
		const daySessionTimes = getSessionsForDay(dayIndex).map((s) => s.time);
		const availableTimeSlots = timeSlots
			.map((_, index) => index)
			.filter((timeIndex) => !daySessionTimes.includes(timeIndex));

		if (availableTimeSlots.length === 0) {
			toast.info("No hay horarios disponibles para marcar como ocupado");
			return;
		}

		// Create busy sessions for all available slots
		const busySessions = availableTimeSlots.map((timeIndex) => ({
			clientId: "0",
			workerId: selectedWorker.id,
			turnId: turn.id,
			day: dayIndex,
			time: timeIndex,
		}));

		bulkCreateSessions(
			{ sessions: busySessions },
			{
				onSuccess: () => {
					toast.success(
						`Día marcado como ocupado (${busySessions.length} horarios)`,
					);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const handleFreeDayBusy = (dayIndex: number) => {
		const busySessions = getBusySessionsForDay(dayIndex);

		if (busySessions.length === 0) {
			toast.info("No hay horarios ocupados para liberar");
			return;
		}

		const busySessionIds = busySessions.map((session) => session.id);

		bulkDeleteSessions(
			{ ids: busySessionIds },
			{
				onSuccess: () => {
					toast.success(`Día liberado (${busySessionIds.length} horarios)`);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (!over) {
			console.log("Element dropped outside of droppable area");
			return;
		}

		// Check if the element didn't change position
		if (active.id === over.id) {
			// Parse the IDs to get day and time indices for handleCellClick
			const parts = String(active.id).split("-").map(Number);
			if (parts.length === 2) {
				const [dayIndex, timeIndex] = parts as [number, number];
				if (!Number.isNaN(dayIndex) && !Number.isNaN(timeIndex)) {
					handleCellClick(dayIndex, timeIndex);
				}
			}
			return;
		}

		// Parse the drag source and drop target
		const sourceParts = String(active.id).split("-").map(Number);
		const targetParts = String(over.id).split("-").map(Number);

		if (sourceParts.length !== 2 || targetParts.length !== 2) {
			return;
		}

		const [sourceDayIndex, sourceTimeIndex] = sourceParts as [number, number];
		const [targetDayIndex, targetTimeIndex] = targetParts as [number, number];

		// Validate that all indices are valid numbers
		if (
			Number.isNaN(sourceDayIndex) ||
			Number.isNaN(sourceTimeIndex) ||
			Number.isNaN(targetDayIndex) ||
			Number.isNaN(targetTimeIndex)
		) {
			return;
		}

		// Get the session being dragged
		const draggedSession = getSessionForSlot(sourceDayIndex, sourceTimeIndex);
		if (!draggedSession) {
			return;
		}

		// Get the session at the target location (if any)
		const targetSession = getSessionForSlot(targetDayIndex, targetTimeIndex);

		// If there's a session at the target location, delete it first
		if (targetSession) {
			deleteSession(targetSession.id, {
				onSuccess: () => {
					// After deleting the target session, update the dragged session
					updateSession(
						{
							id: draggedSession.id,
							clientId: draggedSession.client.id,
							workerId: draggedSession.worker.id,
							turnId: draggedSession.turnId,
							day: targetDayIndex,
							time: targetTimeIndex,
							note: draggedSession.note,
						},
						{
							onSuccess: () => {
								toast.success("Sesión movida exitosamente");
							},
							onError: (error) => {
								console.log(error);
								const { error: errorResult } = getErrorResult(error);
								toast.error(errorResult.message);
							},
						},
					);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			});
		} else {
			// No session at target, just update the dragged session
			updateSession(
				{
					id: draggedSession.id,
					clientId: draggedSession.client.id,
					workerId: draggedSession.worker.id,
					turnId: draggedSession.turnId,
					day: targetDayIndex,
					time: targetTimeIndex,
					note: draggedSession.note,
				},
				{
					onSuccess: () => {
						toast.success("Sesión movida exitosamente");
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				},
			);
		}
	};

	useEffect(() => {
		if (error) {
			const { error: errorResult } = getErrorResult(error);
			toast.error(errorResult.message);
		}
	}, [error]);

	return {
		days,
		sessionModal,
		timeSlots,
		getSessionForSlot,
		getSessionsForSlot,
		showNames,
		minutesToTimeString,
		selectedWorker,
		seeAll,
		handleCellClick,
		handleModalClose,
		hasAvailableSlotsForDay,
		hasBusySessionsForDay,
		handleMakeDayBusy,
		handleFreeDayBusy,
		handleDragEnd,
	};
}
