import { createFileRoute } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import ClientWorkerSelects from "~/session/components/ClientWorkerSelects";
import ScheduleSelect from "~/session/components/ScheduleSelect";
import SessionScheduleGrid from "~/session/components/SessionScheduleGrid";
import TurnSelect from "~/session/components/TurnSelect";
import { sessionStore } from "~/session/store/session";

export const Route = createFileRoute("/_authed/admin/sessions/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Sesiones",
			},
		],
	}),
});

function RouteComponent() {
	const {
		selectedSchedule,
		selectedTurn,
		selectedClient,
		selectedWorker,
		seeAll,
	} = useStore(sessionStore);

	return (
		<div className="container mx-auto">
			<div className="space-y-4">
				{/* Combined Selection Controls */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body ">
						<div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
							{/* Schedule Selection */}
							<div className="space-y-2">
								<ScheduleSelect />
							</div>

							{/* Turn Selection */}
							<div className="space-y-2">
								{selectedSchedule ? (
									<TurnSelect schedule={selectedSchedule} />
								) : (
									<div className="form-control w-full opacity-50">
										<label htmlFor="turn-disabled" className="label">
											<span className="label-text">Turno</span>
										</label>
										<select
											id="turn-disabled"
											className="select select-bordered w-full"
											disabled
										>
											<option>Selecciona un horario primero</option>
										</select>
									</div>
								)}
							</div>

							{/* Client/Worker Selection */}
						</div>
						<div className="flex flex-col">
							{selectedTurn ? (
								<div className="space-y-3">
									<ClientWorkerSelects />
								</div>
							) : (
								<div className="form-control w-full opacity-50">
									<div className="label">
										<span className="label-text">Cliente/Trabajador</span>
									</div>
									<div className="select select-bordered flex w-full items-center text-base-content/50">
										Selecciona un turno primero
									</div>
								</div>
							)}
						</div>
						{/* Status indicator */}
						{selectedTurn &&
							selectedSchedule &&
							(selectedClient || selectedWorker || seeAll) && (
								<div className="mt-3 border-base-300 border-t pt-3">
									<div className="text-base-content/70 text-sm">
										<span className="font-medium">
											{seeAll
												? "Ver todas las sesiones"
												: selectedClient
													? `Cliente: ${selectedClient.person.name} ${selectedClient.person.fatherLastName}`
													: `Trabajador: ${selectedWorker?.person.name} ${selectedWorker?.person.fatherLastName}`}
										</span>
										<span className="mx-2">•</span>
										<span>Horario: {selectedSchedule.name}</span>
										<span className="mx-2">•</span>
										<span>Turno: {selectedTurn.name}</span>
									</div>
								</div>
							)}
					</div>
				</div>

				{/* Schedule Grid - Main Focus */}
				{selectedTurn &&
					selectedSchedule &&
					(selectedClient || selectedWorker || seeAll) && (
						<div className="card flex-1 bg-base-100">
							<div className="card-body">
								<SessionScheduleGrid
									schedule={selectedSchedule}
									turn={selectedTurn}
								/>
							</div>
						</div>
					)}
			</div>
		</div>
	);
}
